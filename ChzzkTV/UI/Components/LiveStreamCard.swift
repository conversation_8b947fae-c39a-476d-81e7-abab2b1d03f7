//
//  LiveStreamCard.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/16/25.
//

import SwiftUI

struct LiveStreamCard: View {
    let stream: UILiveStream?
    
    var body: some View {
        VStack(spacing: 0) {
            ThumbnailView(thumbnail: stream)
            
            LiveStreamCardInfoView(stream: stream)
                .padding(Constants.cardPadding)
        }
        #if os(iOS)
        .background(Color(.secondarySystemBackground))
        #endif
        .clipShape(.rect(cornerRadius: Constants.cornerRadius))
        .fixedSize(horizontal: true, vertical: false)
        .overlay(alignment: .topLeading) {
            if let stream = stream { // Only show overlay for non-empty streams
                ViewerCountOverlayView(label: stream.viewerCountFormatted)
                    .padding(Constants.cardPadding)
            }
        }
    }
}

struct LiveStreamCardInfoView: View {
    let stream: UILiveStream?
        
    var body: some View {
        VStack(alignment: .leading, spacing: Constants.cardInfoSpacing) {
            Text(stream?.category ?? "")
                .font(.caption)
                .foregroundStyle(.secondary)
            
            Text(stream?.title ?? "")
                .bold()
                #if os(tvOS)
                .font(.body)
                #else
                .font(.callout)
                #endif
                .lineLimit(2)
                .frame(maxWidth: .infinity, alignment: .leading)

            HStack(spacing: 8) {
                if let channel = stream?.channel, let url = channel.imageUrl {
                    ChannelImage(url: url)
                }
                
                Text(stream?.channel?.name ?? "")
                    .font(.caption)
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

#Preview("Loading State") {
    LiveStreamCard(stream: nil)
        .padding()
}

#Preview("Loaded State") {
    LiveStreamCard(stream: StreamPreview.sampleUILiveStream)
        .padding()
}

#Preview("Grid Layout") {
    ScrollView {
        LazyVGrid(columns: [GridItem(.adaptive(minimum: Constants.videoCardWidth))], spacing: Constants.cardSpacing) {
            // Loaded state
            ForEach(0..<3) { _ in
                LiveStreamCard(stream: StreamPreview.sampleUILiveStream)
            }
        }
        .padding()
    }
}
