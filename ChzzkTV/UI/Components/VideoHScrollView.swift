import SwiftUI

struct VideoHScrollView: View {
    let videos: [UIVideo]
    @Binding var selectedVideo: UIVideo?
    var onUnfollowed: ((UIChannel) -> Void)?
    
    @Environment(\.channelService) private var channelService
    
    @State private var followStatus: [String: Bool] = [:]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Constants.cardSpacing) {
                ForEach(videos) { video in
                    Button {
                        selectedVideo = video
                    } label: {
                        VideoCard(video: video)
                            .frame(
                                width: Constants.videoCardWidth,
                                height: Constants.videoCardWidth * 0.75 + 80 // 4:3 aspect ratio for thumbnail + space for text
                            )
                            .clipped()
                    }
#if os(tvOS)
                    .buttonStyle(.card)
#else
                    .buttonStyle(.plain)
#endif
                    .contextMenu {
                        contextMenu(video: video)
                    }
                }
            }
            .padding(.horizontal, Constants.cardPadding)
            .padding(.top, Constants.cardPadding * 2)
#if os(tvOS)
            .padding(.bottom, Constants.cardPadding * 6)
#else
            .padding(.bottom, Constants.cardPadding)
#endif
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for video in videos {
                if let channel = video.channel {
                    followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
                }
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(video: UIVideo) -> some View {
        if let channel = video.channel {
            CardContextMenu(
                channel: channel,
                followStatus: Binding(
                    get: { followStatus[channel.id] ?? false },
                    set: { followStatus[channel.id] = $0 }
                ),
                onFollowStatusChanged: { newStatus in
                    if !newStatus {
                        onUnfollowed?(channel)
                    }
                }
            )
            .onAppear {
                if followStatus[channel.id] == nil {
                    followStatus[channel.id] = channelService.isFollowing(channelId: channel.id)
                }
            }
        }
    }
}

#Preview {
    VStack(alignment: .leading) {
        Text("Latest Videos")
            .font(.title2)
            .fontWeight(.bold)
            .padding(.horizontal)
        
        VideoHScrollView(
            videos: StreamPreview.createSampleUIVideos(count: 10),
            selectedVideo: .constant(nil)
        )
        .padding()
    }
    .frame(width: .infinity, height: 300)
}
