import SwiftUI
import SwiftData

struct VideoCard: View {
    let video: UIVideo
    var hideChannelName: Bool = false
    
    var body: some View {
        VStack(spacing: 0) {
            ZStack(alignment: .topLeading) {
                ThumbnailView(thumbnail: video)
                
                VideoCardOverlay(video: video)
                    .padding(Constants.cardPadding)
            }
            
            VideoCardInfo(
                video: video,
                hideChannelName: hideChannelName
            )
            .padding(Constants.cardPadding)
            
            // Progress bar
            if let progress = video.progress, progress > 0 {
                GeometryReader { geometry in
                    Rectangle()
                        .fill(.chzzk)
                        .frame(width: geometry.size.width * progress, height: 3)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(height: 3)
            }
        }
        .background(.ultraThinMaterial)
        .clipShape(.rect(cornerRadius: Constants.cornerRadius))
    }
}

struct VideoCardOverlay: View {
    let video: UIVideo
    
    var body: some View {
        VStack(alignment: .leading) {
            Text(video.formattedDuration)
                .font(.caption)
                .bold()
                .fontWeight(.medium)
                .padding(.horizontal, 6)
                .padding(.vertical, 3)
                .background(.black)
                .cornerRadius(4)
            
            Spacer()
            
            Text(video.relativeDate)
                .font(.caption)
                .bold()
                .fontWeight(.medium)
                .padding(.horizontal, 6)
                .padding(.vertical, 3)
                .background(.black)
                .cornerRadius(4)
        }
    }
}

struct VideoCardInfo: View {
    let video: UIVideo
    var hideChannelName: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: Constants.cardInfoSpacing) {
            if let category = video.category {
                Text(category)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Text(video.title)
                .bold()
                #if os(tvOS)
                .font(.body)
                #else
                .font(.callout)
                #endif
                .lineLimit(2)
//                .frame(maxWidth: .infinity, alignment: .leading)
            
            if let channel = video.channel, !hideChannelName {
                HStack(spacing: 8) {
                    if let url = channel.imageUrl {
                        ChannelImage(url: url)
                    }
                    
                    Text(channel.name)
                        .font(.caption)
                        .lineLimit(1)
                }
            }
        }
//        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

#Preview {
    VideoCard(video: StreamPreview.sampleUIVideo)
        .frame(
            width: Constants.videoCardWidth,
            height: Constants.videoCardWidth * 9/16)
        .padding()
}
